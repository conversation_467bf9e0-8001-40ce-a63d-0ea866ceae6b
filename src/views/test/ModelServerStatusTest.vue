<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">模型服务状态组件测试</h1>
      
      <!-- 使用默认数据 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">默认数据显示</h2>
        <ModelServerStatus />
      </div>
      
      <!-- 使用自定义数据 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">自定义数据显示</h2>
        <ModelServerStatus :server-data="customServerData" />
      </div>
      
      <!-- 错误状态 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">错误状态显示</h2>
        <ModelServerStatus :server-data="errorServerData" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import ModelServerStatus from '@/views/security/statistics/model/components/ModelServerStatus.vue';

  const customServerData = ref({
    modelName: '安全帽检测模型',
    modelNameEn: 'helmet-detection',
    modelVersion: 'v1.8.2',
    modelRunIp: '*************',
    collectTime: '2025-05-20 14:25:10',
    cpuPercentage: '75',
    memoryPercentage: '85',
    gpuPercentage: '60',
    remark: '',
    version: 1,
    serverName: '服务器B',
    status: 'normal' as const,
  });

  const errorServerData = ref({
    modelName: '人员识别模型',
    modelNameEn: 'person-detection',
    modelVersion: 'v2.1.0',
    modelRunIp: '*************',
    collectTime: '2025-05-20 14:20:05',
    cpuPercentage: '95',
    memoryPercentage: '92',
    gpuPercentage: '88',
    remark: '',
    version: 1,
    serverName: '服务器C',
    status: 'error' as const,
  });
</script>
