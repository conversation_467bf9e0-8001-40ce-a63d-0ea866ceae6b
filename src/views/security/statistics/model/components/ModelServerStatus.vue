<template>
  <div class="model-server-status bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 标题和筛选控件 -->
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold text-gray-800">模型服务状态</h3>
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">IP:</span>
          <a-select
            v-model:value="selectedIp"
            placeholder="选择IP"
            style="width: 150px"
            @change="handleIpChange"
          >
            <a-select-option value="">所有IP</a-select-option>
            <a-select-option value="*************">*************</a-select-option>
            <a-select-option value="*************">*************</a-select-option>
          </a-select>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">模型:</span>
          <a-select
            v-model:value="selectedModel"
            placeholder="选择模型"
            style="width: 150px"
            @change="handleModelChange"
          >
            <a-select-option value="">所有模型</a-select-option>
            <a-select-option value="穿戴检测模型">穿戴检测模型</a-select-option>
            <a-select-option value="安全帽检测模型">安全帽检测模型</a-select-option>
          </a-select>
        </div>
      </div>
    </div>

    <!-- 模型服务卡片列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="(server, index) in serverList"
        :key="index"
        class="server-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <!-- 卡片头部 -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center space-x-2">
            <!-- 状态指示器 -->
            <div :class="['w-3 h-3 rounded-full', getStatusColor(server.status)]"></div>
            <div>
              <h4 class="font-medium text-gray-900">{{ server.modelName }}</h4>
              <p class="text-sm text-gray-500">{{ server.modelRunIp }}</p>
            </div>
          </div>
          <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {{ server.modelVersion }}
          </span>
        </div>

        <!-- 资源占用环形图 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex-1">
            <div :ref="(el) => setChartRef(el, index)" class="w-24 h-24"></div>
          </div>
          <div class="flex-1 space-y-2 text-sm">
            <div class="flex items-center justify-between">
              <span class="text-gray-600">CPU:</span>
              <span class="font-medium text-blue-600">{{ server.cpuPercentage }}%</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-gray-600">内存:</span>
              <span class="font-medium text-green-600">{{ server.memoryPercentage }}%</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-gray-600">GPU:</span>
              <span class="font-medium text-orange-600">{{ server.gpuPercentage }}%</span>
            </div>
          </div>
        </div>

        <!-- 最后更新时间 -->
        <div class="text-xs text-gray-500 mb-3">
          最后更新: {{ formatTime(server.updateTime) }}
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-2">
          <a-button size="small" type="text" @click="handleView(server)">
            <template #icon>
              <Icon icon="ant-design:eye-outlined" />
            </template>
          </a-button>
          <a-button size="small" type="text" @click="handleRefresh(server)">
            <template #icon>
              <Icon icon="ant-design:reload-outlined" />
            </template>
          </a-button>
          <a-button size="small" type="text" danger @click="handleDelete(server)">
            <template #icon>
              <Icon icon="ant-design:delete-outlined" />
            </template>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="serverList.length === 0" class="text-center py-12">
      <Icon icon="ant-design:inbox-outlined" class="text-4xl text-gray-400 mb-2" />
      <p class="text-gray-500">暂无模型服务数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { Select as ASelect, Button as AButton, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { selectModelServerStats } from '@/api/security/modelStats';
  import type { BiModelServerHealthVo } from '@/api/security/modelStats/model';

  defineOptions({ name: 'ModelServerStatus' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const selectedIp = ref('');
  const selectedModel = ref('');
  const serverList = ref<BiModelServerHealthVo[]>([]);
  const chartRefs = ref<HTMLElement[]>([]);

  // 设置图表引用
  function setChartRef(el: HTMLElement | null, index: number) {
    if (el) {
      chartRefs.value[index] = el;
    }
  }

  // 获取状态颜色
  function getStatusColor(status?: string) {
    switch (status) {
      case 'normal':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  }

  // 格式化时间
  function formatTime(time?: string) {
    if (!time) return '--';
    return new Date(time).toLocaleString();
  }

  // 创建资源占用环形图
  function createResourceChart(element: HTMLElement, server: BiModelServerHealthVo) {
    const { setOptions } = useECharts(ref(element));

    const option = {
      series: [
        {
          type: 'pie',
          radius: ['60%', '90%'],
          center: ['50%', '50%'],
          data: [
            {
              value: parseFloat(server.cpuPercentage || '0'),
              name: 'CPU',
              itemStyle: { color: '#3b82f6' },
            },
            {
              value: parseFloat(server.memoryPercentage || '0'),
              name: '内存',
              itemStyle: { color: '#10b981' },
            },
            {
              value: parseFloat(server.gpuPercentage || '0'),
              name: 'GPU',
              itemStyle: { color: '#f59e0b' },
            },
          ],
          label: { show: false },
          labelLine: { show: false },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };

    setOptions(option);
  }

  // 获取服务器列表数据
  async function fetchServerList() {
    try {
      const params = {
        modelName: selectedModel.value || undefined,
        // 这里可以根据需要添加IP筛选参数
      };

      const response = await selectModelServerStats(params);
      serverList.value = response || [];

      // 等待DOM更新后创建图表
      await nextTick();
      serverList.value.forEach((server, index) => {
        const chartElement = chartRefs.value[index];
        if (chartElement) {
          createResourceChart(chartElement, server);
        }
      });
    } catch (error) {
      console.error('获取模型服务状态失败:', error);
      serverList.value = [];
    }
  }

  // IP变化处理
  function handleIpChange() {
    fetchServerList();
  }

  // 模型变化处理
  function handleModelChange() {
    fetchServerList();
  }

  // 查看操作
  function handleView(server: BiModelServerHealthVo) {
    message.info(`查看服务器: ${server.modelName}`);
  }

  // 刷新操作
  function handleRefresh(server: BiModelServerHealthVo) {
    message.info(`刷新服务器: ${server.modelName}`);
    fetchServerList();
  }

  // 删除操作
  function handleDelete(server: BiModelServerHealthVo) {
    message.warning(`删除服务器: ${server.modelName}`);
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    () => {
      fetchServerList();
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchServerList();
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => fetchServerList(),
  });
</script>

<style scoped>
  .server-card {
    transition: all 0.3s ease;
  }

  .server-card:hover {
    transform: translateY(-1px);
  }
</style>
