<template>
  <div class="model-server-status bg-white rounded-lg p-6 shadow-sm">
    <!-- 卡片头部 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-3">
        <!-- 状态指示器 -->
        <div :class="['w-4 h-4 rounded-full', getStatusColor(serverData.status)]"></div>
        <h3 class="text-lg font-medium text-gray-900">
          {{ serverData.modelName }} - {{ serverData.serverName || '服务器A' }}
        </h3>
      </div>
      <span class="text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full">
        {{ serverData.modelVersion || 'v2.3.0' }}
      </span>
    </div>

    <!-- 资源使用率圆形进度条 -->
    <div class="flex items-center justify-center space-x-12 mb-8">
      <div class="text-center">
        <div ref="cpuChartRef" class="w-24 h-24 mx-auto"></div>
        <div class="mt-2 text-sm text-gray-600 font-medium">CPU</div>
      </div>
      <div class="text-center">
        <div ref="memoryChartRef" class="w-24 h-24 mx-auto"></div>
        <div class="mt-2 text-sm text-gray-600 font-medium">内存</div>
      </div>
      <div class="text-center">
        <div ref="gpuChartRef" class="w-24 h-24 mx-auto"></div>
        <div class="mt-2 text-sm text-gray-600 font-medium">GPU</div>
      </div>
    </div>

    <!-- 底部信息和操作 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-500">
        最后更新: {{ formatTime(serverData.collectTime) }}
      </div>
      <div class="flex items-center space-x-2">
        <a-button size="small" type="text" @click="handleView">
          <template #icon>
            <Icon icon="ant-design:eye-outlined" />
          </template>
        </a-button>
        <a-button size="small" type="text" @click="handleRefresh">
          <template #icon>
            <Icon icon="ant-design:reload-outlined" />
          </template>
        </a-button>
        <a-button size="small" type="text" danger @click="handleStop">
          <template #icon>
            <Icon icon="ant-design:stop-outlined" />
          </template>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-12">
      <Icon icon="ant-design:loading-outlined" class="text-2xl text-gray-400 mb-2 animate-spin" />
      <p class="text-gray-500">加载中...</p>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !serverData.modelName" class="text-center py-12">
      <Icon icon="ant-design:inbox-outlined" class="text-4xl text-gray-400 mb-2" />
      <p class="text-gray-500">暂无模型服务数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { Button as AButton, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { selectModelServerStats } from '@/api/security/modelStats';
  import type { BiModelServerHealthVo } from '@/api/security/modelStats/model';

  defineOptions({ name: 'ModelServerStatus' });

  // 扩展服务器数据接口
  interface ExtendedServerData extends BiModelServerHealthVo {
    serverName?: string;
    status?: 'normal' | 'error' | 'warning';
  }

  interface Props {
    searchParams?: any;
    serverData?: ExtendedServerData;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
    serverData: undefined,
  });

  const loading = ref(false);
  const serverData = ref<ExtendedServerData>({
    modelName: '穿戴检测模型',
    modelNameEn: 'wearable-detection',
    modelVersion: 'v2.3.0',
    modelRunIp: '*************',
    collectTime: new Date().toISOString(),
    cpuPercentage: '45',
    memoryPercentage: '60',
    gpuPercentage: '30',
    remark: '',
    version: 1,
    serverName: '服务器A',
    status: 'normal',
  });

  // 图表引用
  const cpuChartRef = ref<HTMLElement>();
  const memoryChartRef = ref<HTMLElement>();
  const gpuChartRef = ref<HTMLElement>();

  // 获取状态颜色
  function getStatusColor(status?: string) {
    switch (status) {
      case 'normal':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  }

  // 格式化时间
  function formatTime(time?: string) {
    if (!time) return '--';
    try {
      const date = new Date(time);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
    } catch {
      return time;
    }
  }

  // 创建圆形进度条
  function createGaugeChart(element: HTMLElement, value: number, color: string, label: string) {
    const { setOptions } = useECharts(ref(element as HTMLDivElement));

    const option: any = {
      series: [
        {
          type: 'gauge',
          radius: '90%',
          startAngle: 90,
          endAngle: -270,
          pointer: { show: false },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 0,
              color: color,
            },
          },
          axisLine: {
            lineStyle: {
              width: 8,
              color: [[1, '#E5E7EB']],
            },
          },
          splitLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
          detail: {
            valueAnimation: true,
            formatter: '{value}%',
            color: '#374151',
            fontSize: 18,
            fontWeight: 'bold',
            offsetCenter: [0, 0],
          },
          data: [{ value: value, name: label }],
        },
      ],
    };

    setOptions(option);
  }

  // 获取服务器数据
  async function fetchServerData() {
    if (props.serverData) {
      serverData.value = { ...props.serverData };
      await updateCharts();
      return;
    }

    try {
      loading.value = true;
      const params = {
        modelName: undefined,
      };

      const response = await selectModelServerStats(params);
      const serverList = response || [];

      if (serverList.length > 0) {
        // 取第一个服务器数据
        serverData.value = {
          ...serverList[0],
          serverName: '服务器A',
          status: 'normal' as const,
        };
        await updateCharts();
      }
    } catch (error) {
      console.error('获取模型服务状态失败:', error);
      message.error('获取服务器状态失败');
    } finally {
      loading.value = false;
    }
  }

  // 更新所有图表
  async function updateCharts() {
    await nextTick();

    if (cpuChartRef.value) {
      createGaugeChart(
        cpuChartRef.value,
        parseFloat(serverData.value.cpuPercentage || '0'),
        '#3B82F6',
        'CPU'
      );
    }

    if (memoryChartRef.value) {
      createGaugeChart(
        memoryChartRef.value,
        parseFloat(serverData.value.memoryPercentage || '0'),
        '#10B981',
        '内存'
      );
    }

    if (gpuChartRef.value) {
      createGaugeChart(
        gpuChartRef.value,
        parseFloat(serverData.value.gpuPercentage || '0'),
        '#F59E0B',
        'GPU'
      );
    }
  }

  // 查看操作
  function handleView() {
    message.info(`查看服务器: ${serverData.value.modelName}`);
  }

  // 刷新操作
  function handleRefresh() {
    message.info(`刷新服务器: ${serverData.value.modelName}`);
    fetchServerData();
  }

  // 停止操作
  function handleStop() {
    message.warning(`停止服务器: ${serverData.value.modelName}`);
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    () => {
      fetchServerData();
    },
    { deep: true, immediate: false },
  );

  // 监听传入的服务器数据变化
  watch(
    () => props.serverData,
    (newData) => {
      if (newData) {
        serverData.value = { ...newData } as any;
        updateCharts();
      }
    },
    { deep: true, immediate: true },
  );

  onMounted(() => {
    fetchServerData();
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => fetchServerData(),
  });
</script>

<style scoped>
  .server-card {
    transition: all 0.3s ease;
  }

  .server-card:hover {
    transform: translateY(-1px);
  }
</style>
